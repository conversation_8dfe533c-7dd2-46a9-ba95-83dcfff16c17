const request = require('supertest');
const { app } = require('../server');
const { pool } = require('../config/database');

describe('Authentication API', () => {
  beforeAll(async () => {
    // 清理测试数据
    await pool.execute('DELETE FROM users WHERE username IN (?, ?)', ['test_chenxu', 'test_jingyi']);
  });

  afterAll(async () => {
    // 清理测试数据
    await pool.execute('DELETE FROM users WHERE username IN (?, ?)', ['test_chenxu', 'test_jingyi']);
    await pool.end();
  });

  describe('POST /api/auth/login', () => {
    test('应该拒绝错误的密码', async () => {
      const response = await request(app)
        .post('/api/auth/login')
        .send({ password: 'wrong_password' });

      expect(response.status).toBe(401);
      expect(response.body.success).toBe(false);
      expect(response.body.message).toContain('密码错误');
    });

    test('应该接受正确的密码并返回token', async () => {
      const response = await request(app)
        .post('/api/auth/login')
        .send({ password: '240702' });

      expect(response.status).toBe(200);
      expect(response.body.success).toBe(true);
      expect(response.body.token).toBeDefined();
      expect(response.body.user).toBeDefined();
      expect(response.body.partner).toBeDefined();
    });

    test('应该拒绝空密码', async () => {
      const response = await request(app)
        .post('/api/auth/login')
        .send({});

      expect(response.status).toBe(400);
    });
  });

  describe('GET /api/auth/me', () => {
    let authToken;

    beforeAll(async () => {
      // 获取认证token
      const loginResponse = await request(app)
        .post('/api/auth/login')
        .send({ password: '240702' });
      
      authToken = loginResponse.body.token;
    });

    test('应该返回当前用户信息', async () => {
      const response = await request(app)
        .get('/api/auth/me')
        .set('Authorization', `Bearer ${authToken}`);

      expect(response.status).toBe(200);
      expect(response.body.success).toBe(true);
      expect(response.body.user).toBeDefined();
      expect(response.body.user.id).toBeDefined();
      expect(response.body.user.username).toBeDefined();
    });

    test('应该拒绝无效的token', async () => {
      const response = await request(app)
        .get('/api/auth/me')
        .set('Authorization', 'Bearer invalid_token');

      expect(response.status).toBe(403);
      expect(response.body.success).toBe(false);
    });

    test('应该拒绝缺少token的请求', async () => {
      const response = await request(app)
        .get('/api/auth/me');

      expect(response.status).toBe(401);
      expect(response.body.success).toBe(false);
    });
  });
});
