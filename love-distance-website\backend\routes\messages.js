const express = require('express');
const crypto = require('crypto');
const { pool } = require('../config/database');
const jwt = require('jsonwebtoken');
const router = express.Router();

// JWT验证中间件
const authenticateToken = (req, res, next) => {
  const authHeader = req.headers['authorization'];
  const token = authHeader && authHeader.split(' ')[1];

  if (!token) {
    return res.status(401).json({ success: false, message: '未提供访问令牌' });
  }

  jwt.verify(token, process.env.JWT_SECRET, (err, user) => {
    if (err) {
      return res.status(403).json({ success: false, message: '令牌无效' });
    }
    req.user = user;
    next();
  });
};

// 加密消息
function encryptMessage(text) {
  try {
    const algorithm = 'aes-256-cbc';
    const key = crypto.scryptSync(process.env.ENCRYPTION_KEY || 'love_encryption_key_240702', 'salt', 32);
    const iv = crypto.randomBytes(16);

    const cipher = crypto.createCipheriv(algorithm, key, iv);
    let encrypted = cipher.update(text, 'utf8', 'hex');
    encrypted += cipher.final('hex');

    return iv.toString('hex') + ':' + encrypted;
  } catch (error) {
    console.error('加密失败:', error);
    throw new Error('消息加密失败');
  }
}

// 解密消息
function decryptMessage(encryptedText) {
  try {
    const algorithm = 'aes-256-cbc';
    const key = crypto.scryptSync(process.env.ENCRYPTION_KEY || 'love_encryption_key_240702', 'salt', 32);

    const textParts = encryptedText.split(':');
    if (textParts.length !== 2) {
      throw new Error('加密数据格式错误');
    }

    const iv = Buffer.from(textParts[0], 'hex');
    const encrypted = textParts[1];

    const decipher = crypto.createDecipheriv(algorithm, key, iv);
    let decrypted = decipher.update(encrypted, 'hex', 'utf8');
    decrypted += decipher.final('utf8');

    return decrypted;
  } catch (error) {
    console.error('解密失败:', error);
    return '[加密消息]'; // 如果解密失败，返回提示文本
  }
}

// 发送消息
router.post('/send', authenticateToken, async (req, res) => {
  try {
    const { content, messageType = 'text', isEncrypted = true } = req.body;
    
    if (!content) {
      return res.status(400).json({ success: false, message: '消息内容不能为空' });
    }

    const receiverId = req.user.partnerId;
    let finalContent = content;
    
    // 如果需要加密
    if (isEncrypted && messageType === 'text') {
      finalContent = encryptMessage(content);
    }

    // 保存消息到数据库
    const [result] = await pool.execute(
      `INSERT INTO messages (sender_id, receiver_id, content, message_type, is_encrypted) 
       VALUES (?, ?, ?, ?, ?)`,
      [req.user.userId, receiverId, finalContent, messageType, isEncrypted]
    );

    // 获取刚插入的消息
    const [messages] = await pool.execute(
      `SELECT m.*, u.real_name as sender_name 
       FROM messages m 
       JOIN users u ON m.sender_id = u.id 
       WHERE m.id = ?`,
      [result.insertId]
    );

    const message = messages[0];
    
    // 如果是加密消息，解密后返回
    let displayContent = message.content;
    if (message.is_encrypted && messageType === 'text') {
      displayContent = decryptMessage(message.content);
    }

    res.json({
      success: true,
      message: '消息发送成功',
      data: {
        id: message.id,
        senderId: message.sender_id,
        receiverId: message.receiver_id,
        content: displayContent,
        messageType: message.message_type,
        isEncrypted: message.is_encrypted,
        isRead: message.is_read,
        senderName: message.sender_name,
        createdAt: message.created_at
      }
    });

  } catch (error) {
    console.error('发送消息错误:', error);
    res.status(500).json({ success: false, message: '服务器错误' });
  }
});

// 获取聊天记录
router.get('/chat', authenticateToken, async (req, res) => {
  try {
    const { limit = 50, offset = 0, before } = req.query;
    
    let query = `
      SELECT m.*, 
             sender.real_name as sender_name,
             receiver.real_name as receiver_name
      FROM messages m
      JOIN users sender ON m.sender_id = sender.id
      JOIN users receiver ON m.receiver_id = receiver.id
      WHERE (m.sender_id = ? AND m.receiver_id = ?) 
         OR (m.sender_id = ? AND m.receiver_id = ?)
    `;
    
    const queryParams = [
      req.user.userId, req.user.partnerId,
      req.user.partnerId, req.user.userId
    ];
    
    if (before) {
      query += ' AND m.created_at < ?';
      queryParams.push(before);
    }
    
    query += ' ORDER BY m.created_at DESC LIMIT ? OFFSET ?';
    queryParams.push(parseInt(limit), parseInt(offset));

    const [messages] = await pool.execute(query, queryParams);

    // 解密消息内容
    const decryptedMessages = messages.map(message => {
      let displayContent = message.content;
      
      if (message.is_encrypted && message.message_type === 'text') {
        displayContent = decryptMessage(message.content);
      }
      
      return {
        id: message.id,
        senderId: message.sender_id,
        receiverId: message.receiver_id,
        content: displayContent,
        messageType: message.message_type,
        isEncrypted: message.is_encrypted,
        isRead: message.is_read,
        senderName: message.sender_name,
        receiverName: message.receiver_name,
        createdAt: message.created_at
      };
    });

    res.json({
      success: true,
      messages: decryptedMessages.reverse(), // 按时间正序返回
      hasMore: messages.length === parseInt(limit)
    });

  } catch (error) {
    console.error('获取聊天记录错误:', error);
    res.status(500).json({ success: false, message: '服务器错误' });
  }
});

// 标记消息为已读
router.put('/read/:messageId', authenticateToken, async (req, res) => {
  try {
    const { messageId } = req.params;

    // 只能标记发给自己的消息为已读
    await pool.execute(
      'UPDATE messages SET is_read = TRUE WHERE id = ? AND receiver_id = ?',
      [messageId, req.user.userId]
    );

    res.json({ success: true, message: '消息已标记为已读' });

  } catch (error) {
    console.error('标记消息已读错误:', error);
    res.status(500).json({ success: false, message: '服务器错误' });
  }
});

// 批量标记消息为已读
router.put('/read-all', authenticateToken, async (req, res) => {
  try {
    await pool.execute(
      'UPDATE messages SET is_read = TRUE WHERE receiver_id = ? AND is_read = FALSE',
      [req.user.userId]
    );

    res.json({ success: true, message: '所有消息已标记为已读' });

  } catch (error) {
    console.error('批量标记消息已读错误:', error);
    res.status(500).json({ success: false, message: '服务器错误' });
  }
});

// 获取未读消息数量
router.get('/unread-count', authenticateToken, async (req, res) => {
  try {
    const [result] = await pool.execute(
      'SELECT COUNT(*) as unread_count FROM messages WHERE receiver_id = ? AND is_read = FALSE',
      [req.user.userId]
    );

    res.json({
      success: true,
      unreadCount: result[0].unread_count
    });

  } catch (error) {
    console.error('获取未读消息数量错误:', error);
    res.status(500).json({ success: false, message: '服务器错误' });
  }
});

// 删除消息
router.delete('/:messageId', authenticateToken, async (req, res) => {
  try {
    const { messageId } = req.params;

    // 只能删除自己发送的消息
    const [result] = await pool.execute(
      'DELETE FROM messages WHERE id = ? AND sender_id = ?',
      [messageId, req.user.userId]
    );

    if (result.affectedRows === 0) {
      return res.status(404).json({ success: false, message: '消息不存在或无权删除' });
    }

    res.json({ success: true, message: '消息删除成功' });

  } catch (error) {
    console.error('删除消息错误:', error);
    res.status(500).json({ success: false, message: '服务器错误' });
  }
});

// 搜索消息
router.get('/search', authenticateToken, async (req, res) => {
  try {
    const { keyword, limit = 20, offset = 0 } = req.query;
    
    if (!keyword) {
      return res.status(400).json({ success: false, message: '请提供搜索关键词' });
    }

    // 注意：由于消息是加密的，这里的搜索功能有限
    // 实际应用中可能需要在客户端进行搜索，或者使用其他搜索策略
    const [messages] = await pool.execute(
      `SELECT m.*, 
              sender.real_name as sender_name,
              receiver.real_name as receiver_name
       FROM messages m
       JOIN users sender ON m.sender_id = sender.id
       JOIN users receiver ON m.receiver_id = receiver.id
       WHERE ((m.sender_id = ? AND m.receiver_id = ?) 
              OR (m.sender_id = ? AND m.receiver_id = ?))
         AND (m.content LIKE ? OR sender.real_name LIKE ?)
       ORDER BY m.created_at DESC
       LIMIT ? OFFSET ?`,
      [
        req.user.userId, req.user.partnerId,
        req.user.partnerId, req.user.userId,
        `%${keyword}%`, `%${keyword}%`,
        parseInt(limit), parseInt(offset)
      ]
    );

    // 解密消息内容
    const decryptedMessages = messages.map(message => {
      let displayContent = message.content;
      
      if (message.is_encrypted && message.message_type === 'text') {
        try {
          displayContent = decryptMessage(message.content);
        } catch (error) {
          displayContent = '[加密消息]';
        }
      }
      
      return {
        id: message.id,
        senderId: message.sender_id,
        receiverId: message.receiver_id,
        content: displayContent,
        messageType: message.message_type,
        isEncrypted: message.is_encrypted,
        isRead: message.is_read,
        senderName: message.sender_name,
        receiverName: message.receiver_name,
        createdAt: message.created_at
      };
    });

    res.json({
      success: true,
      messages: decryptedMessages,
      keyword: keyword
    });

  } catch (error) {
    console.error('搜索消息错误:', error);
    res.status(500).json({ success: false, message: '服务器错误' });
  }
});

// 获取聊天统计信息
router.get('/stats', authenticateToken, async (req, res) => {
  try {
    // 总消息数
    const [totalResult] = await pool.execute(
      `SELECT COUNT(*) as total_messages FROM messages 
       WHERE (sender_id = ? AND receiver_id = ?) OR (sender_id = ? AND receiver_id = ?)`,
      [req.user.userId, req.user.partnerId, req.user.partnerId, req.user.userId]
    );

    // 今日消息数
    const [todayResult] = await pool.execute(
      `SELECT COUNT(*) as today_messages FROM messages 
       WHERE ((sender_id = ? AND receiver_id = ?) OR (sender_id = ? AND receiver_id = ?))
         AND DATE(created_at) = CURDATE()`,
      [req.user.userId, req.user.partnerId, req.user.partnerId, req.user.userId]
    );

    // 我发送的消息数
    const [sentResult] = await pool.execute(
      'SELECT COUNT(*) as sent_messages FROM messages WHERE sender_id = ?',
      [req.user.userId]
    );

    // 收到的消息数
    const [receivedResult] = await pool.execute(
      'SELECT COUNT(*) as received_messages FROM messages WHERE receiver_id = ?',
      [req.user.userId]
    );

    // 未读消息数
    const [unreadResult] = await pool.execute(
      'SELECT COUNT(*) as unread_messages FROM messages WHERE receiver_id = ? AND is_read = FALSE',
      [req.user.userId]
    );

    res.json({
      success: true,
      stats: {
        totalMessages: totalResult[0].total_messages,
        todayMessages: todayResult[0].today_messages,
        sentMessages: sentResult[0].sent_messages,
        receivedMessages: receivedResult[0].received_messages,
        unreadMessages: unreadResult[0].unread_messages
      }
    });

  } catch (error) {
    console.error('获取聊天统计错误:', error);
    res.status(500).json({ success: false, message: '服务器错误' });
  }
});

module.exports = router;