const jwt = require('jsonwebtoken');
const { pool } = require('../config/database');

// JWT验证中间件
const authenticateToken = (req, res, next) => {
  const authHeader = req.headers['authorization'];
  const token = authHeader && authHeader.split(' ')[1];

  if (!token) {
    return res.status(401).json({ 
      success: false, 
      message: '未提供访问令牌',
      code: 'NO_TOKEN'
    });
  }

  jwt.verify(token, process.env.JWT_SECRET, (err, user) => {
    if (err) {
      let message = '令牌无效';
      let code = 'INVALID_TOKEN';
      
      if (err.name === 'TokenExpiredError') {
        message = '令牌已过期';
        code = 'TOKEN_EXPIRED';
      } else if (err.name === 'JsonWebTokenError') {
        message = '令牌格式错误';
        code = 'MALFORMED_TOKEN';
      }
      
      return res.status(403).json({ 
        success: false, 
        message: message,
        code: code
      });
    }
    
    req.user = user;
    next();
  });
};

// 验证用户是否为情侣关系
const validateCoupleRelation = async (req, res, next) => {
  try {
    const userId = req.user.userId;
    
    // 获取用户信息和伴侣信息
    const [users] = await pool.execute(
      'SELECT id, partner_id, is_active FROM users WHERE id = ?',
      [userId]
    );

    if (users.length === 0) {
      return res.status(404).json({
        success: false,
        message: '用户不存在',
        code: 'USER_NOT_FOUND'
      });
    }

    const user = users[0];

    if (!user.is_active) {
      return res.status(403).json({
        success: false,
        message: '账户已被禁用',
        code: 'ACCOUNT_DISABLED'
      });
    }

    if (!user.partner_id) {
      return res.status(403).json({
        success: false,
        message: '未建立情侣关系',
        code: 'NO_PARTNER'
      });
    }

    // 验证伴侣关系是否互相确认
    const [partners] = await pool.execute(
      'SELECT id, partner_id, is_active FROM users WHERE id = ?',
      [user.partner_id]
    );

    if (partners.length === 0 || partners[0].partner_id !== userId) {
      return res.status(403).json({
        success: false,
        message: '情侣关系未确认',
        code: 'PARTNER_NOT_CONFIRMED'
      });
    }

    if (!partners[0].is_active) {
      return res.status(403).json({
        success: false,
        message: '伴侣账户已被禁用',
        code: 'PARTNER_DISABLED'
      });
    }

    // 将伴侣ID添加到请求对象中
    req.user.partnerId = user.partner_id;
    next();

  } catch (error) {
    console.error('验证情侣关系错误:', error);
    res.status(500).json({
      success: false,
      message: '服务器错误',
      code: 'SERVER_ERROR'
    });
  }
};

// 生成访问令牌
const generateAccessToken = (user) => {
  return jwt.sign(
    { 
      userId: user.id,
      username: user.username,
      realName: user.real_name
    },
    process.env.JWT_SECRET,
    { expiresIn: process.env.JWT_EXPIRES_IN || '24h' }
  );
};

// 生成刷新令牌
const generateRefreshToken = (user) => {
  return jwt.sign(
    { 
      userId: user.id,
      type: 'refresh'
    },
    process.env.JWT_REFRESH_SECRET || process.env.JWT_SECRET,
    { expiresIn: process.env.JWT_REFRESH_EXPIRES_IN || '7d' }
  );
};

// 验证刷新令牌
const verifyRefreshToken = (token) => {
  return new Promise((resolve, reject) => {
    jwt.verify(token, process.env.JWT_REFRESH_SECRET || process.env.JWT_SECRET, (err, decoded) => {
      if (err) {
        reject(err);
      } else if (decoded.type !== 'refresh') {
        reject(new Error('Invalid token type'));
      } else {
        resolve(decoded);
      }
    });
  });
};

// 检查用户权限（可选中间件）
const checkPermission = (requiredPermission) => {
  return async (req, res, next) => {
    try {
      const userId = req.user.userId;
      
      // 获取用户权限信息
      const [users] = await pool.execute(
        'SELECT role, permissions FROM users WHERE id = ?',
        [userId]
      );

      if (users.length === 0) {
        return res.status(404).json({
          success: false,
          message: '用户不存在',
          code: 'USER_NOT_FOUND'
        });
      }

      const user = users[0];
      const userPermissions = user.permissions ? JSON.parse(user.permissions) : [];

      // 管理员拥有所有权限
      if (user.role === 'admin') {
        return next();
      }

      // 检查是否有所需权限
      if (!userPermissions.includes(requiredPermission)) {
        return res.status(403).json({
          success: false,
          message: '权限不足',
          code: 'INSUFFICIENT_PERMISSION'
        });
      }

      next();

    } catch (error) {
      console.error('检查权限错误:', error);
      res.status(500).json({
        success: false,
        message: '服务器错误',
        code: 'SERVER_ERROR'
      });
    }
  };
};

// 限制访问频率中间件
const rateLimitByUser = (maxRequests = 100, windowMs = 15 * 60 * 1000) => {
  const userRequests = new Map();

  return (req, res, next) => {
    const userId = req.user?.userId;
    if (!userId) {
      return next();
    }

    const now = Date.now();
    const userKey = `user_${userId}`;
    
    if (!userRequests.has(userKey)) {
      userRequests.set(userKey, { count: 1, resetTime: now + windowMs });
      return next();
    }

    const userLimit = userRequests.get(userKey);
    
    if (now > userLimit.resetTime) {
      userLimit.count = 1;
      userLimit.resetTime = now + windowMs;
      return next();
    }

    if (userLimit.count >= maxRequests) {
      return res.status(429).json({
        success: false,
        message: '请求过于频繁，请稍后再试',
        code: 'RATE_LIMIT_EXCEEDED',
        retryAfter: Math.ceil((userLimit.resetTime - now) / 1000)
      });
    }

    userLimit.count++;
    next();
  };
};

// 记录用户活动中间件
const logUserActivity = async (req, res, next) => {
  try {
    const userId = req.user?.userId;
    if (userId) {
      // 更新用户最后活动时间
      await pool.execute(
        'UPDATE users SET last_active = CURRENT_TIMESTAMP WHERE id = ?',
        [userId]
      );

      // 可选：记录详细的活动日志
      if (process.env.LOG_USER_ACTIVITY === 'true') {
        await pool.execute(
          `INSERT INTO user_activity_logs (user_id, action, endpoint, ip_address, user_agent) 
           VALUES (?, ?, ?, ?, ?)`,
          [
            userId,
            req.method,
            req.originalUrl,
            req.ip || req.connection.remoteAddress,
            req.get('User-Agent') || ''
          ]
        );
      }
    }
    next();
  } catch (error) {
    console.error('记录用户活动错误:', error);
    // 不阻断请求，继续执行
    next();
  }
};

// 验证请求来源中间件（可选）
const validateOrigin = (allowedOrigins = []) => {
  return (req, res, next) => {
    const origin = req.get('Origin');
    
    if (!origin) {
      return next();
    }

    if (allowedOrigins.length === 0 || allowedOrigins.includes(origin)) {
      return next();
    }

    return res.status(403).json({
      success: false,
      message: '请求来源不被允许',
      code: 'INVALID_ORIGIN'
    });
  };
};

// 组合中间件：完整的认证和授权流程
const fullAuth = [
  authenticateToken,
  validateCoupleRelation,
  logUserActivity
];

// 基础认证中间件（不验证情侣关系）
const basicAuth = [
  authenticateToken,
  logUserActivity
];

module.exports = {
  authenticateToken,
  validateCoupleRelation,
  generateAccessToken,
  generateRefreshToken,
  verifyRefreshToken,
  checkPermission,
  rateLimitByUser,
  logUserActivity,
  validateOrigin,
  fullAuth,
  basicAuth
};