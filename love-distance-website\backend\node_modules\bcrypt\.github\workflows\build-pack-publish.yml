name: Prebuildify, package, publish

on:
  push:
    branches: [ master ]
  pull_request:
    branches: [ master ]
  release:
    types: [ prereleased, released ]

jobs:
  build-linux:
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v4
      # This is unsafe, but we really don't use any other native dependencies
      - run: npm ci
      - run: docker run -u $(id -u):$(id -g) -v `pwd`:/input -w /input ghcr.io/prebuild/almalinux-devtoolset11 npx prebuildify --napi --tag-libc --strip --target=node@18.0.0
      - run: docker run -u $(id -u):$(id -g) -v `pwd`:/input -w /input ghcr.io/prebuild/alpine npx prebuildify --napi --tag-libc --strip --target=node@18.0.0
      - run: docker run -u $(id -u):$(id -g) -v `pwd`:/input -w /input ghcr.io/prebuild/linux-armv7 npx prebuildify --napi --tag-libc --strip --target=node@18.0.0
      - run: docker run -u $(id -u):$(id -g) -v `pwd`:/input -w /input ghcr.io/prebuild/linux-armv7l-musl npx prebuildify --napi --tag-libc --strip --target=node@18.0.0
      - run: docker run -u $(id -u):$(id -g) -v `pwd`:/input -w /input ghcr.io/prebuild/linux-arm64 npx prebuildify --napi --tag-libc --strip --target=node@18.0.0
      - run: docker run -u $(id -u):$(id -g) -v `pwd`:/input -w /input ghcr.io/prebuild/linux-arm64-musl npx prebuildify --napi --tag-libc --strip --target=node@18.0.0
      - run: find prebuilds
      - uses: actions/upload-artifact@v4
        with:
          name: prebuild-linux
          path: ./prebuilds/

  build-windows:
    runs-on: windows-latest
    steps:
      - uses: actions/checkout@v4
      - uses: actions/setup-node@v4
        with:
          node-version: 20
          registry-url: 'https://registry.npmjs.org'
      - run: npm ci
      - run: npx prebuildify --napi --strip --arch=x64 --target=node@18.0.0
      - run: npx prebuildify --napi --strip --arch=arm64 --target=node@20.0.0
      - run: dir prebuilds
      - uses: actions/upload-artifact@v4
        with:
          name: prebuild-windows
          path: ./prebuilds/

  build-macos:
    runs-on: macos-latest
    steps:
      - uses: actions/checkout@v4
      - uses: actions/setup-node@v4
        with:
          node-version: 18
          registry-url: 'https://registry.npmjs.org'
      - run: npm ci
      - run: npx prebuildify --napi --strip --arch=arm64 --target=node@18.0.0
      - run: npx prebuildify --napi --strip --arch=x64 --target=node@18.0.0
      - run: find prebuilds
      - uses: actions/upload-artifact@v4
        with:
          name: prebuild-macos
          path: ./prebuilds/

  pack:
    needs:
      - build-linux
      - build-windows
      - build-macos
    runs-on: ubuntu-latest
    outputs:
        PACK_FILE: ${{ steps.pack.outputs.PACK_FILE }}
    steps:
      - uses: actions/checkout@v4
      - uses: actions/download-artifact@v4
        with:
          path: /tmp/prebuilds/
      - name: Coalesce prebuilds from build matrix
        run: |
          mkdir prebuilds
          for d in /tmp/prebuilds/*; do
            cp -Rav $d/* prebuilds/
          done
      - run: chmod a+x prebuilds/*/*.node && find prebuilds -executable -type f
      - id: pack
        name: Prepare NPM package
        run: |
          echo "PACK_FILE=$(npm pack)" >> "$GITHUB_OUTPUT"
      - uses: actions/upload-artifact@v4
        with:
          name: package-tgz
          path: ${{ steps.pack.outputs.PACK_FILE }}
          if-no-files-found: 'error'

  test-package:
    needs: pack
    strategy:
      matrix:
        node-version: [ 18, 20, 22, 23 ]
        os: [ ubuntu-latest, windows-latest, macos-latest ]
    runs-on: ${{ matrix.os }}
    steps:
      - uses: actions/setup-node@v4
        with:
          node-version: ${{ matrix.node-version }}
          registry-url: 'https://registry.npmjs.org'
      - uses: actions/download-artifact@v4
        with:
          name: package-tgz
      - run: npm install ${{ needs.pack.outputs.PACK_FILE }}
      - run: node -e "const b = require('bcrypt'); const h = b.hashSync('hello', 10); console.log(h, b.compareSync('hello', h))"
