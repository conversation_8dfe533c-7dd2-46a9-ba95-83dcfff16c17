const multer = require('multer');
const path = require('path');
const fs = require('fs');

// 确保上传目录存在
const uploadDir = path.join(__dirname, '../uploads');
const avatarDir = path.join(uploadDir, 'avatars');
const albumDir = path.join(uploadDir, 'albums');

[uploadDir, avatarDir, albumDir].forEach(dir => {
  if (!fs.existsSync(dir)) {
    fs.mkdirSync(dir, { recursive: true });
  }
});

// 文件存储配置
const storage = multer.diskStorage({
  destination: function (req, file, cb) {
    let uploadPath = uploadDir;
    
    // 根据文件类型选择存储目录
    if (req.route.path.includes('avatar')) {
      uploadPath = avatarDir;
    } else if (req.route.path.includes('album')) {
      uploadPath = albumDir;
    }
    
    cb(null, uploadPath);
  },
  filename: function (req, file, cb) {
    // 生成唯一文件名
    const uniqueSuffix = Date.now() + '-' + Math.round(Math.random() * 1E9);
    const ext = path.extname(file.originalname);
    cb(null, file.fieldname + '-' + uniqueSuffix + ext);
  }
});

// 文件过滤器
const fileFilter = (req, file, cb) => {
  // 检查文件类型
  const allowedTypes = /jpeg|jpg|png|gif|webp/;
  const extname = allowedTypes.test(path.extname(file.originalname).toLowerCase());
  const mimetype = allowedTypes.test(file.mimetype);

  if (mimetype && extname) {
    return cb(null, true);
  } else {
    cb(new Error('只允许上传图片文件 (jpeg, jpg, png, gif, webp)'));
  }
};

// 创建multer实例
const upload = multer({
  storage: storage,
  limits: {
    fileSize: 10 * 1024 * 1024, // 10MB限制
    files: 10 // 最多10个文件
  },
  fileFilter: fileFilter
});

// 头像上传中间件（单文件）
const uploadAvatar = upload.single('avatar');

// 相册上传中间件（多文件）
const uploadAlbum = upload.array('photos', 10);

// 单张照片上传中间件
const uploadSingle = upload.single('photo');

// 错误处理中间件
const handleUploadError = (error, req, res, next) => {
  if (error instanceof multer.MulterError) {
    if (error.code === 'LIMIT_FILE_SIZE') {
      return res.status(400).json({
        success: false,
        message: '文件大小超过限制（最大10MB）'
      });
    } else if (error.code === 'LIMIT_FILE_COUNT') {
      return res.status(400).json({
        success: false,
        message: '文件数量超过限制（最多10个文件）'
      });
    } else if (error.code === 'LIMIT_UNEXPECTED_FILE') {
      return res.status(400).json({
        success: false,
        message: '意外的文件字段'
      });
    }
  } else if (error) {
    return res.status(400).json({
      success: false,
      message: error.message || '文件上传失败'
    });
  }
  next();
};

// 文件信息处理函数
const processFileInfo = (file, baseUrl = '') => {
  if (!file) return null;
  
  return {
    filename: file.filename,
    originalName: file.originalname,
    mimetype: file.mimetype,
    size: file.size,
    url: `${baseUrl}/uploads/${path.basename(path.dirname(file.path))}/${file.filename}`,
    uploadedAt: new Date()
  };
};

// 批量处理文件信息
const processFilesInfo = (files, baseUrl = '') => {
  if (!files || !Array.isArray(files)) return [];
  
  return files.map(file => processFileInfo(file, baseUrl));
};

// 删除文件函数
const deleteFile = (filePath) => {
  return new Promise((resolve, reject) => {
    fs.unlink(filePath, (err) => {
      if (err && err.code !== 'ENOENT') {
        reject(err);
      } else {
        resolve();
      }
    });
  });
};

// 删除多个文件
const deleteFiles = async (filePaths) => {
  const deletePromises = filePaths.map(filePath => deleteFile(filePath));
  await Promise.allSettled(deletePromises);
};

// 获取文件完整路径
const getFilePath = (filename, type = 'albums') => {
  const baseDir = type === 'avatars' ? avatarDir : albumDir;
  return path.join(baseDir, filename);
};

// 检查文件是否存在
const fileExists = (filePath) => {
  return fs.existsSync(filePath);
};

// 获取文件统计信息
const getFileStats = (filePath) => {
  try {
    return fs.statSync(filePath);
  } catch (error) {
    return null;
  }
};

// 清理过期文件（可选功能）
const cleanupExpiredFiles = async (directory, maxAge = 30 * 24 * 60 * 60 * 1000) => {
  try {
    const files = fs.readdirSync(directory);
    const now = Date.now();
    
    for (const file of files) {
      const filePath = path.join(directory, file);
      const stats = getFileStats(filePath);
      
      if (stats && (now - stats.mtime.getTime()) > maxAge) {
        await deleteFile(filePath);
        console.log(`已清理过期文件: ${file}`);
      }
    }
  } catch (error) {
    console.error('清理过期文件失败:', error);
  }
};

// 获取目录大小
const getDirectorySize = (directory) => {
  try {
    const files = fs.readdirSync(directory);
    let totalSize = 0;
    
    for (const file of files) {
      const filePath = path.join(directory, file);
      const stats = getFileStats(filePath);
      if (stats && stats.isFile()) {
        totalSize += stats.size;
      }
    }
    
    return totalSize;
  } catch (error) {
    return 0;
  }
};

// 格式化文件大小
const formatFileSize = (bytes) => {
  if (bytes === 0) return '0 Bytes';
  
  const k = 1024;
  const sizes = ['Bytes', 'KB', 'MB', 'GB'];
  const i = Math.floor(Math.log(bytes) / Math.log(k));
  
  return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
};

module.exports = {
  uploadAvatar,
  uploadAlbum,
  uploadSingle,
  handleUploadError,
  processFileInfo,
  processFilesInfo,
  deleteFile,
  deleteFiles,
  getFilePath,
  fileExists,
  getFileStats,
  cleanupExpiredFiles,
  getDirectorySize,
  formatFileSize,
  uploadDir,
  avatarDir,
  albumDir
};