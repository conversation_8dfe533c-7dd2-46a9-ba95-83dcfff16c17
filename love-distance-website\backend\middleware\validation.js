const { body, validationResult } = require('express-validator');

// 通用验证错误处理中间件
const handleValidationErrors = (req, res, next) => {
  const errors = validationResult(req);
  if (!errors.isEmpty()) {
    return res.status(400).json({
      success: false,
      message: '输入数据验证失败',
      errors: errors.array()
    });
  }
  next();
};

// 登录验证规则
const validateLogin = [
  body('password')
    .notEmpty()
    .withMessage('密码不能为空')
    .isLength({ min: 1, max: 50 })
    .withMessage('密码长度必须在1-50个字符之间'),
  handleValidationErrors
];

// 消息发送验证规则
const validateMessage = [
  body('content')
    .notEmpty()
    .withMessage('消息内容不能为空')
    .isLength({ max: 2000 })
    .withMessage('消息内容不能超过2000个字符'),
  body('messageType')
    .optional()
    .isIn(['text', 'image', 'emoji'])
    .withMessage('消息类型无效'),
  body('isEncrypted')
    .optional()
    .isBoolean()
    .withMessage('加密标志必须是布尔值'),
  handleValidationErrors
];

// 纪念日创建验证规则
const validateAnniversary = [
  body('title')
    .notEmpty()
    .withMessage('标题不能为空')
    .isLength({ max: 100 })
    .withMessage('标题不能超过100个字符'),
  body('description')
    .optional()
    .isLength({ max: 500 })
    .withMessage('描述不能超过500个字符'),
  body('anniversaryDate')
    .notEmpty()
    .withMessage('日期不能为空')
    .isISO8601()
    .withMessage('日期格式不正确'),
  body('category')
    .optional()
    .isIn(['birthday', 'anniversary', 'holiday', 'custom'])
    .withMessage('类别无效'),
  body('isYearly')
    .optional()
    .isBoolean()
    .withMessage('年度重复标志必须是布尔值'),
  body('reminderDays')
    .optional()
    .isInt({ min: 0, max: 365 })
    .withMessage('提醒天数必须是0-365之间的整数'),
  handleValidationErrors
];

// 相册项更新验证规则
const validateAlbumUpdate = [
  body('title')
    .optional()
    .isLength({ max: 100 })
    .withMessage('标题不能超过100个字符'),
  body('description')
    .optional()
    .isLength({ max: 500 })
    .withMessage('描述不能超过500个字符'),
  body('location')
    .optional()
    .isLength({ max: 255 })
    .withMessage('位置信息不能超过255个字符'),
  body('takenAt')
    .optional()
    .isISO8601()
    .withMessage('拍摄时间格式不正确'),
  handleValidationErrors
];

// 位置更新验证规则
const validateLocation = [
  body('latitude')
    .notEmpty()
    .withMessage('纬度不能为空')
    .isFloat({ min: -90, max: 90 })
    .withMessage('纬度必须在-90到90之间'),
  body('longitude')
    .notEmpty()
    .withMessage('经度不能为空')
    .isFloat({ min: -180, max: 180 })
    .withMessage('经度必须在-180到180之间'),
  body('address')
    .optional()
    .isLength({ max: 255 })
    .withMessage('地址不能超过255个字符'),
  body('accuracy')
    .optional()
    .isFloat({ min: 0 })
    .withMessage('精度必须是非负数'),
  handleValidationErrors
];

// 搜索参数验证规则
const validateSearch = [
  body('keyword')
    .optional()
    .isLength({ max: 100 })
    .withMessage('搜索关键词不能超过100个字符'),
  body('limit')
    .optional()
    .isInt({ min: 1, max: 100 })
    .withMessage('限制数量必须在1-100之间'),
  body('offset')
    .optional()
    .isInt({ min: 0 })
    .withMessage('偏移量必须是非负整数'),
  handleValidationErrors
];

// 通用ID参数验证
const validateId = (paramName = 'id') => [
  (req, res, next) => {
    const id = req.params[paramName];
    if (!id || isNaN(parseInt(id)) || parseInt(id) <= 0) {
      return res.status(400).json({
        success: false,
        message: `无效的${paramName}参数`
      });
    }
    next();
  }
];

// 文件上传验证
const validateFileUpload = (req, res, next) => {
  if (!req.files || req.files.length === 0) {
    return res.status(400).json({
      success: false,
      message: '请选择要上传的文件'
    });
  }
  
  // 检查文件大小
  const maxSize = 10 * 1024 * 1024; // 10MB
  for (const file of req.files) {
    if (file.size > maxSize) {
      return res.status(400).json({
        success: false,
        message: '文件大小不能超过10MB'
      });
    }
  }
  
  next();
};

module.exports = {
  validateLogin,
  validateMessage,
  validateAnniversary,
  validateAlbumUpdate,
  validateLocation,
  validateSearch,
  validateId,
  validateFileUpload,
  handleValidationErrors
};
