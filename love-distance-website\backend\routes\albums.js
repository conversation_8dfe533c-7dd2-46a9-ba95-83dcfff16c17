const express = require('express');
const multer = require('multer');
const path = require('path');
const fs = require('fs');
const { pool } = require('../config/database');
const jwt = require('jsonwebtoken');
const router = express.Router();

// JWT验证中间件
const authenticateToken = (req, res, next) => {
  const authHeader = req.headers['authorization'];
  const token = authHeader && authHeader.split(' ')[1];

  if (!token) {
    return res.status(401).json({ success: false, message: '未提供访问令牌' });
  }

  jwt.verify(token, process.env.JWT_SECRET, (err, user) => {
    if (err) {
      return res.status(403).json({ success: false, message: '令牌无效' });
    }
    req.user = user;
    next();
  });
};

// 确保上传目录存在
const albumsDir = path.join(__dirname, '../uploads/albums');
if (!fs.existsSync(albumsDir)) {
  fs.mkdirSync(albumsDir, { recursive: true });
}

// 相册文件上传配置
const albumStorage = multer.diskStorage({
  destination: (req, file, cb) => {
    cb(null, albumsDir);
  },
  filename: (req, file, cb) => {
    const uniqueSuffix = Date.now() + '-' + Math.round(Math.random() * 1E9);
    const ext = path.extname(file.originalname);
    cb(null, `album-${req.user.userId}-${uniqueSuffix}${ext}`);
  }
});

const albumUpload = multer({
  storage: albumStorage,
  limits: {
    fileSize: 10 * 1024 * 1024, // 10MB
    files: 10 // 最多10个文件
  },
  fileFilter: (req, file, cb) => {
    // 允许的文件扩展名
    const allowedExtensions = ['jpg', 'jpeg', 'png', 'gif', 'mp4', 'mov', 'avi'];
    // 允许的MIME类型
    const allowedMimeTypes = [
      'image/jpeg', 'image/jpg', 'image/png', 'image/gif',
      'video/mp4', 'video/quicktime', 'video/x-msvideo'
    ];

    const fileExt = path.extname(file.originalname).toLowerCase().slice(1);
    const mimeType = file.mimetype.toLowerCase();

    // 同时验证扩展名和MIME类型
    if (allowedExtensions.includes(fileExt) && allowedMimeTypes.includes(mimeType)) {
      cb(null, true);
    } else {
      cb(new Error('只支持图片和视频文件'), false);
    }
  }
});

// 上传照片/视频
router.post('/upload', authenticateToken, albumUpload.array('files', 10), async (req, res) => {
  try {
    if (!req.files || req.files.length === 0) {
      return res.status(400).json({ success: false, message: '请选择要上传的文件' });
    }

    const { title, description, location, takenAt } = req.body;
    const uploadedFiles = [];

    for (const file of req.files) {
      const imageUrl = `/uploads/albums/${file.filename}`;
      const imageType = file.mimetype.startsWith('video/') ? 'video' : 'image';
      
      // 保存到数据库
      const [result] = await pool.execute(
        `INSERT INTO albums (user_id, title, description, image_url, image_type, file_size, location, taken_at) 
         VALUES (?, ?, ?, ?, ?, ?, ?, ?)`,
        [
          req.user.userId,
          title || `${req.user.username}的${imageType === 'video' ? '视频' : '照片'}`,
          description || '',
          imageUrl,
          imageType,
          file.size,
          location || null,
          takenAt || new Date()
        ]
      );

      uploadedFiles.push({
        id: result.insertId,
        title: title || `${req.user.username}的${imageType === 'video' ? '视频' : '照片'}`,
        description: description || '',
        imageUrl: imageUrl,
        imageType: imageType,
        fileSize: file.size,
        location: location || null,
        takenAt: takenAt || new Date(),
        originalName: file.originalname
      });
    }

    res.json({
      success: true,
      message: `成功上传 ${uploadedFiles.length} 个文件`,
      files: uploadedFiles
    });

  } catch (error) {
    console.error('上传文件错误:', error);
    res.status(500).json({ success: false, message: '服务器错误' });
  }
});

// 获取相册列表
router.get('/', authenticateToken, async (req, res) => {
  try {
    const { 
      limit = 20, 
      offset = 0, 
      type = 'all', // all, image, video
      sortBy = 'created_at', // created_at, taken_at
      sortOrder = 'DESC' 
    } = req.query;

    let query = `
      SELECT a.*, u.real_name as uploader_name 
      FROM albums a 
      JOIN users u ON a.user_id = u.id 
      WHERE a.user_id IN (?, ?)
    `;
    
    const queryParams = [req.user.userId, req.user.partnerId];

    // 按类型筛选
    if (type !== 'all') {
      query += ' AND a.image_type = ?';
      queryParams.push(type);
    }

    // 排序
    const allowedSortBy = ['created_at', 'taken_at', 'file_size'];
    const allowedSortOrder = ['ASC', 'DESC'];
    
    if (allowedSortBy.includes(sortBy) && allowedSortOrder.includes(sortOrder.toUpperCase())) {
      query += ` ORDER BY a.${sortBy} ${sortOrder.toUpperCase()}`;
    } else {
      query += ' ORDER BY a.created_at DESC';
    }

    query += ' LIMIT ? OFFSET ?';
    queryParams.push(parseInt(limit), parseInt(offset));

    const [albums] = await pool.execute(query, queryParams);

    res.json({
      success: true,
      albums: albums.map(album => ({
        id: album.id,
        userId: album.user_id,
        title: album.title,
        description: album.description,
        imageUrl: album.image_url,
        imageType: album.image_type,
        fileSize: album.file_size,
        location: album.location,
        takenAt: album.taken_at,
        createdAt: album.created_at,
        uploaderName: album.uploader_name
      })),
      hasMore: albums.length === parseInt(limit)
    });

  } catch (error) {
    console.error('获取相册列表错误:', error);
    res.status(500).json({ success: false, message: '服务器错误' });
  }
});

// 获取单个相册项详情
router.get('/:id', authenticateToken, async (req, res) => {
  try {
    const { id } = req.params;

    const [albums] = await pool.execute(
      `SELECT a.*, u.real_name as uploader_name 
       FROM albums a 
       JOIN users u ON a.user_id = u.id 
       WHERE a.id = ? AND a.user_id IN (?, ?)`,
      [id, req.user.userId, req.user.partnerId]
    );

    if (albums.length === 0) {
      return res.status(404).json({ success: false, message: '相册项不存在' });
    }

    const album = albums[0];

    res.json({
      success: true,
      album: {
        id: album.id,
        userId: album.user_id,
        title: album.title,
        description: album.description,
        imageUrl: album.image_url,
        imageType: album.image_type,
        fileSize: album.file_size,
        location: album.location,
        takenAt: album.taken_at,
        createdAt: album.created_at,
        uploaderName: album.uploader_name
      }
    });

  } catch (error) {
    console.error('获取相册项详情错误:', error);
    res.status(500).json({ success: false, message: '服务器错误' });
  }
});

// 更新相册项信息
router.put('/:id', authenticateToken, async (req, res) => {
  try {
    const { id } = req.params;
    const { title, description, location, takenAt } = req.body;

    // 检查权限（只能修改自己上传的）
    const [albums] = await pool.execute(
      'SELECT id FROM albums WHERE id = ? AND user_id = ?',
      [id, req.user.userId]
    );

    if (albums.length === 0) {
      return res.status(404).json({ success: false, message: '相册项不存在或无权修改' });
    }

    const updateFields = [];
    const updateValues = [];

    if (title !== undefined) {
      updateFields.push('title = ?');
      updateValues.push(title);
    }

    if (description !== undefined) {
      updateFields.push('description = ?');
      updateValues.push(description);
    }

    if (location !== undefined) {
      updateFields.push('location = ?');
      updateValues.push(location);
    }

    if (takenAt !== undefined) {
      updateFields.push('taken_at = ?');
      updateValues.push(takenAt);
    }

    if (updateFields.length === 0) {
      return res.status(400).json({ success: false, message: '没有提供要更新的字段' });
    }

    updateValues.push(id);

    await pool.execute(
      `UPDATE albums SET ${updateFields.join(', ')}, updated_at = CURRENT_TIMESTAMP WHERE id = ?`,
      updateValues
    );

    res.json({ success: true, message: '相册项更新成功' });

  } catch (error) {
    console.error('更新相册项错误:', error);
    res.status(500).json({ success: false, message: '服务器错误' });
  }
});

// 删除相册项
router.delete('/:id', authenticateToken, async (req, res) => {
  try {
    const { id } = req.params;

    // 获取文件信息（只能删除自己上传的）
    const [albums] = await pool.execute(
      'SELECT image_url FROM albums WHERE id = ? AND user_id = ?',
      [id, req.user.userId]
    );

    if (albums.length === 0) {
      return res.status(404).json({ success: false, message: '相册项不存在或无权删除' });
    }

    const album = albums[0];

    // 删除数据库记录
    await pool.execute('DELETE FROM albums WHERE id = ?', [id]);

    // 删除文件
    try {
      const filePath = path.join(__dirname, '..', album.image_url);
      if (fs.existsSync(filePath)) {
        fs.unlinkSync(filePath);
      }
    } catch (fileError) {
      console.error('删除文件失败:', fileError);
      // 即使文件删除失败，也不影响数据库删除的成功
    }

    res.json({ success: true, message: '相册项删除成功' });

  } catch (error) {
    console.error('删除相册项错误:', error);
    res.status(500).json({ success: false, message: '服务器错误' });
  }
});

// 按时间线获取相册
router.get('/timeline/:year/:month?', authenticateToken, async (req, res) => {
  try {
    const { year, month } = req.params;
    
    let query = `
      SELECT a.*, u.real_name as uploader_name 
      FROM albums a 
      JOIN users u ON a.user_id = u.id 
      WHERE a.user_id IN (?, ?) AND YEAR(a.taken_at) = ?
    `;
    
    const queryParams = [req.user.userId, req.user.partnerId, parseInt(year)];

    if (month) {
      query += ' AND MONTH(a.taken_at) = ?';
      queryParams.push(parseInt(month));
    }

    query += ' ORDER BY a.taken_at DESC';

    const [albums] = await pool.execute(query, queryParams);

    // 按日期分组
    const groupedAlbums = {};
    albums.forEach(album => {
      const date = album.taken_at.toISOString().split('T')[0]; // YYYY-MM-DD
      if (!groupedAlbums[date]) {
        groupedAlbums[date] = [];
      }
      groupedAlbums[date].push({
        id: album.id,
        userId: album.user_id,
        title: album.title,
        description: album.description,
        imageUrl: album.image_url,
        imageType: album.image_type,
        fileSize: album.file_size,
        location: album.location,
        takenAt: album.taken_at,
        createdAt: album.created_at,
        uploaderName: album.uploader_name
      });
    });

    res.json({
      success: true,
      year: parseInt(year),
      month: month ? parseInt(month) : null,
      timeline: groupedAlbums,
      totalCount: albums.length
    });

  } catch (error) {
    console.error('获取时间线相册错误:', error);
    res.status(500).json({ success: false, message: '服务器错误' });
  }
});

// 获取相册统计信息
router.get('/stats/summary', authenticateToken, async (req, res) => {
  try {
    // 总数统计
    const [totalResult] = await pool.execute(
      'SELECT COUNT(*) as total_count FROM albums WHERE user_id IN (?, ?)',
      [req.user.userId, req.user.partnerId]
    );

    // 按类型统计
    const [typeResult] = await pool.execute(
      `SELECT image_type, COUNT(*) as count 
       FROM albums 
       WHERE user_id IN (?, ?) 
       GROUP BY image_type`,
      [req.user.userId, req.user.partnerId]
    );

    // 按月份统计（最近12个月）
    const [monthlyResult] = await pool.execute(
      `SELECT 
         DATE_FORMAT(taken_at, '%Y-%m') as month,
         COUNT(*) as count
       FROM albums 
       WHERE user_id IN (?, ?) 
         AND taken_at >= DATE_SUB(NOW(), INTERVAL 12 MONTH)
       GROUP BY DATE_FORMAT(taken_at, '%Y-%m')
       ORDER BY month DESC`,
      [req.user.userId, req.user.partnerId]
    );

    // 存储空间统计
    const [storageResult] = await pool.execute(
      'SELECT SUM(file_size) as total_size FROM albums WHERE user_id IN (?, ?)',
      [req.user.userId, req.user.partnerId]
    );

    // 我上传的数量
    const [myUploadsResult] = await pool.execute(
      'SELECT COUNT(*) as my_uploads FROM albums WHERE user_id = ?',
      [req.user.userId]
    );

    const typeStats = {};
    typeResult.forEach(item => {
      typeStats[item.image_type] = item.count;
    });

    const monthlyStats = {};
    monthlyResult.forEach(item => {
      monthlyStats[item.month] = item.count;
    });

    res.json({
      success: true,
      stats: {
        totalCount: totalResult[0].total_count,
        myUploads: myUploadsResult[0].my_uploads,
        partnerUploads: totalResult[0].total_count - myUploadsResult[0].my_uploads,
        typeStats: typeStats,
        monthlyStats: monthlyStats,
        totalSize: storageResult[0].total_size || 0,
        averageSize: totalResult[0].total_count > 0 ? 
          Math.round((storageResult[0].total_size || 0) / totalResult[0].total_count) : 0
      }
    });

  } catch (error) {
    console.error('获取相册统计错误:', error);
    res.status(500).json({ success: false, message: '服务器错误' });
  }
});

// 搜索相册
router.get('/search/query', authenticateToken, async (req, res) => {
  try {
    const { 
      keyword, 
      type = 'all',
      location,
      dateFrom,
      dateTo,
      limit = 20, 
      offset = 0 
    } = req.query;

    let query = `
      SELECT a.*, u.real_name as uploader_name 
      FROM albums a 
      JOIN users u ON a.user_id = u.id 
      WHERE a.user_id IN (?, ?)
    `;
    
    const queryParams = [req.user.userId, req.user.partnerId];

    // 关键词搜索
    if (keyword) {
      query += ' AND (a.title LIKE ? OR a.description LIKE ?)';
      queryParams.push(`%${keyword}%`, `%${keyword}%`);
    }

    // 类型筛选
    if (type !== 'all') {
      query += ' AND a.image_type = ?';
      queryParams.push(type);
    }

    // 地点筛选
    if (location) {
      query += ' AND a.location LIKE ?';
      queryParams.push(`%${location}%`);
    }

    // 日期范围筛选
    if (dateFrom) {
      query += ' AND a.taken_at >= ?';
      queryParams.push(dateFrom);
    }

    if (dateTo) {
      query += ' AND a.taken_at <= ?';
      queryParams.push(dateTo);
    }

    query += ' ORDER BY a.taken_at DESC LIMIT ? OFFSET ?';
    queryParams.push(parseInt(limit), parseInt(offset));

    const [albums] = await pool.execute(query, queryParams);

    res.json({
      success: true,
      albums: albums.map(album => ({
        id: album.id,
        userId: album.user_id,
        title: album.title,
        description: album.description,
        imageUrl: album.image_url,
        imageType: album.image_type,
        fileSize: album.file_size,
        location: album.location,
        takenAt: album.taken_at,
        createdAt: album.created_at,
        uploaderName: album.uploader_name
      })),
      searchParams: {
        keyword,
        type,
        location,
        dateFrom,
        dateTo
      },
      hasMore: albums.length === parseInt(limit)
    });

  } catch (error) {
    console.error('搜索相册错误:', error);
    res.status(500).json({ success: false, message: '服务器错误' });
  }
});

module.exports = router;