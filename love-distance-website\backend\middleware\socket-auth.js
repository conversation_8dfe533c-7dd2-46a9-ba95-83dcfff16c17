const jwt = require('jsonwebtoken');

// WebSocket身份验证中间件
const authenticateSocket = (socket, next) => {
  try {
    const token = socket.handshake.auth.token || socket.handshake.headers.authorization?.split(' ')[1];
    
    if (!token) {
      return next(new Error('未提供访问令牌'));
    }

    jwt.verify(token, process.env.JWT_SECRET, (err, decoded) => {
      if (err) {
        return next(new Error('令牌无效'));
      }
      
      socket.userId = decoded.userId;
      socket.partnerId = decoded.partnerId;
      socket.username = decoded.username;
      next();
    });
  } catch (error) {
    next(new Error('身份验证失败'));
  }
};

// 验证用户权限
const validateUserPermission = (socket, targetUserId) => {
  return socket.userId === targetUserId || socket.partnerId === targetUserId;
};

// 输入数据验证
const validateMessageData = (data) => {
  if (!data || typeof data !== 'object') {
    throw new Error('无效的消息数据');
  }
  
  if (!data.receiverId || !data.message) {
    throw new Error('缺少必要的消息字段');
  }
  
  if (typeof data.message !== 'string' || data.message.length > 2000) {
    throw new Error('消息内容无效或过长');
  }
  
  return true;
};

const validateLocationData = (data) => {
  if (!data || typeof data !== 'object') {
    throw new Error('无效的位置数据');
  }
  
  if (!data.receiverId || !data.location) {
    throw new Error('缺少必要的位置字段');
  }
  
  const { latitude, longitude } = data.location;
  if (typeof latitude !== 'number' || typeof longitude !== 'number') {
    throw new Error('位置坐标必须是数字');
  }
  
  if (latitude < -90 || latitude > 90 || longitude < -180 || longitude > 180) {
    throw new Error('位置坐标超出有效范围');
  }
  
  return true;
};

module.exports = {
  authenticateSocket,
  validateUserPermission,
  validateMessageData,
  validateLocationData
};
