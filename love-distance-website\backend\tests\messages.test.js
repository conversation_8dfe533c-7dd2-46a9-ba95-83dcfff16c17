const request = require('supertest');
const { app } = require('../server');
const { pool } = require('../config/database');

describe('Messages API', () => {
  let authToken;
  let userId;
  let partnerId;

  beforeAll(async () => {
    // 获取认证token
    const loginResponse = await request(app)
      .post('/api/auth/login')
      .send({ password: '240702' });
    
    authToken = loginResponse.body.token;
    userId = loginResponse.body.user.id;
    partnerId = loginResponse.body.partner.id;
  });

  afterAll(async () => {
    // 清理测试消息
    await pool.execute('DELETE FROM messages WHERE sender_id = ? OR receiver_id = ?', [userId, userId]);
    await pool.end();
  });

  describe('POST /api/messages/send', () => {
    test('应该成功发送文本消息', async () => {
      const messageData = {
        content: '这是一条测试消息',
        messageType: 'text',
        isEncrypted: true
      };

      const response = await request(app)
        .post('/api/messages/send')
        .set('Authorization', `Bearer ${authToken}`)
        .send(messageData);

      expect(response.status).toBe(200);
      expect(response.body.success).toBe(true);
      expect(response.body.data).toBeDefined();
      expect(response.body.data.content).toBe(messageData.content);
      expect(response.body.data.senderId).toBe(userId);
      expect(response.body.data.receiverId).toBe(partnerId);
    });

    test('应该拒绝空消息内容', async () => {
      const response = await request(app)
        .post('/api/messages/send')
        .set('Authorization', `Bearer ${authToken}`)
        .send({ content: '' });

      expect(response.status).toBe(400);
      expect(response.body.success).toBe(false);
    });

    test('应该拒绝未认证的请求', async () => {
      const response = await request(app)
        .post('/api/messages/send')
        .send({ content: '测试消息' });

      expect(response.status).toBe(401);
      expect(response.body.success).toBe(false);
    });
  });

  describe('GET /api/messages/chat', () => {
    beforeAll(async () => {
      // 创建一些测试消息
      await request(app)
        .post('/api/messages/send')
        .set('Authorization', `Bearer ${authToken}`)
        .send({ content: '测试消息1', messageType: 'text' });

      await request(app)
        .post('/api/messages/send')
        .set('Authorization', `Bearer ${authToken}`)
        .send({ content: '测试消息2', messageType: 'text' });
    });

    test('应该返回聊天记录', async () => {
      const response = await request(app)
        .get('/api/messages/chat')
        .set('Authorization', `Bearer ${authToken}`);

      expect(response.status).toBe(200);
      expect(response.body.success).toBe(true);
      expect(response.body.messages).toBeDefined();
      expect(Array.isArray(response.body.messages)).toBe(true);
      expect(response.body.messages.length).toBeGreaterThan(0);
    });

    test('应该支持分页', async () => {
      const response = await request(app)
        .get('/api/messages/chat?limit=1&offset=0')
        .set('Authorization', `Bearer ${authToken}`);

      expect(response.status).toBe(200);
      expect(response.body.success).toBe(true);
      expect(response.body.messages.length).toBeLessThanOrEqual(1);
    });
  });

  describe('GET /api/messages/unread-count', () => {
    test('应该返回未读消息数量', async () => {
      const response = await request(app)
        .get('/api/messages/unread-count')
        .set('Authorization', `Bearer ${authToken}`);

      expect(response.status).toBe(200);
      expect(response.body.success).toBe(true);
      expect(response.body.unreadCount).toBeDefined();
      expect(typeof response.body.unreadCount).toBe('number');
    });
  });

  describe('PUT /api/messages/read-all', () => {
    test('应该标记所有消息为已读', async () => {
      const response = await request(app)
        .put('/api/messages/read-all')
        .set('Authorization', `Bearer ${authToken}`);

      expect(response.status).toBe(200);
      expect(response.body.success).toBe(true);
    });
  });
});
