import React, { useState } from 'react'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Heart, Lock, Star } from 'lucide-react'
import { useToast } from '@/hooks/use-toast'
import { api } from '@/utils/api-config'

interface LoginPageProps {
  onLogin: (success: boolean) => void
}

const LoginPage: React.FC<LoginPageProps> = ({ onLogin }) => {
  const [password, setPassword] = useState('')
  const [isLoading, setIsLoading] = useState(false)
  const { toast } = useToast()

  // 计算恋爱天数
  const loveStartDate = new Date('2024-07-02')
  const today = new Date()
  const daysTogether = Math.floor((today.getTime() - loveStartDate.getTime()) / (1000 * 60 * 60 * 24))

  const handleLogin = async (e: React.FormEvent) => {
    e.preventDefault()
    setIsLoading(true)

    try {
      const data = await api.post<{
        success: boolean;
        token?: string;
        user?: any;
        partner?: any;
        message?: string;
      }>('/api/auth/login', { password })

      if (data.success) {
        // 保存token
        localStorage.setItem('token', data.token!)
        localStorage.setItem('user', JSON.stringify(data.user))
        localStorage.setItem('partner', JSON.stringify(data.partner))

        toast({
          title: "欢迎回到我们的小世界 💕",
          description: "爱你每一天~",
        })
        onLogin(true)
      } else {
        toast({
          title: "密码错误",
          description: data.message || "请输入正确的密码哦~",
          variant: "destructive",
        })
      }
    } catch (error) {
      toast({
        title: "登录失败",
        description: "网络错误，请稍后重试",
        variant: "destructive",
      })
    } finally {
      setIsLoading(false)
    }
  }

  return (
    <div className="min-h-screen flex items-center justify-center p-4 relative overflow-hidden">
      {/* 背景装饰 */}
      <div className="absolute inset-0 overflow-hidden">
        {/* 飘落的爱心动画 */}
        {[...Array(20)].map((_, i) => (
          <Heart
            key={i}
            className={`absolute text-pink-300 opacity-20 animate-pulse`}
            style={{
              left: `${Math.random() * 100}%`,
              top: `${Math.random() * 100}%`,
              animationDelay: `${Math.random() * 3}s`,
              fontSize: `${Math.random() * 20 + 10}px`,
            }}
          />
        ))}
        
        {/* 星星装饰 */}
        {[...Array(15)].map((_, i) => (
          <Star
            key={i}
            className={`absolute text-yellow-300 opacity-30 animate-twinkle`}
            style={{
              left: `${Math.random() * 100}%`,
              top: `${Math.random() * 100}%`,
              animationDelay: `${Math.random() * 2}s`,
              fontSize: `${Math.random() * 15 + 8}px`,
            }}
          />
        ))}
      </div>

      {/* 主登录卡片 */}
      <Card className="w-full max-w-md bg-white/90 backdrop-blur-sm shadow-2xl border-pink-200">
        <CardHeader className="text-center space-y-4">
          <div className="mx-auto w-20 h-20 bg-gradient-to-br from-pink-400 to-rose-400 rounded-full flex items-center justify-center shadow-lg">
            <Heart className="w-10 h-10 text-white animate-pulse" />
          </div>
          <CardTitle className="text-2xl font-bold bg-gradient-to-r from-pink-500 to-rose-500 bg-clip-text text-transparent">
            我们的秘密花园
          </CardTitle>
          <CardDescription className="text-gray-600">
            晨旭 ❤️ 静怡
          </CardDescription>
        </CardHeader>
        
        <CardContent className="space-y-6">
          <form onSubmit={handleLogin} className="space-y-4">
            <div className="space-y-2">
              <div className="relative">
                <Lock className="absolute left-3 top-3 h-4 w-4 text-pink-400" />
                <Input
                  type="password"
                  placeholder="请输入我们的专属密码"
                  value={password}
                  onChange={(e) => setPassword(e.target.value)}
                  className="pl-10 border-pink-200 focus:border-pink-400 focus:ring-pink-400"
                  required
                />
              </div>
            </div>
            
            <Button
              type="submit"
              className="w-full bg-gradient-to-r from-pink-400 to-rose-400 hover:from-pink-500 hover:to-rose-500 text-white shadow-lg"
              disabled={isLoading}
            >
              {isLoading ? (
                <div className="flex items-center space-x-2">
                  <div className="w-4 h-4 border-2 border-white border-t-transparent rounded-full animate-spin" />
                  <span>进入中...</span>
                </div>
              ) : (
                <div className="flex items-center space-x-2">
                  <Heart className="w-4 h-4" />
                  <span>进入我们的世界</span>
                </div>
              )}
            </Button>
          </form>
          
          {/* 恋爱天数显示 */}
          <div className="text-center p-4 bg-gradient-to-r from-pink-50 to-rose-50 rounded-lg border border-pink-100">
            <p className="text-sm text-gray-600 mb-1">我们在一起</p>
            <p className="text-2xl font-bold text-pink-500">{daysTogether}</p>
            <p className="text-sm text-gray-600">天了 💕</p>
          </div>
          
          <div className="text-center text-xs text-gray-500">
            <p>💝 只属于我们两个人的小世界 💝</p>
          </div>
        </CardContent>
      </Card>
    </div>
  )
}

export default LoginPage