import React, { createContext, useContext, useReducer, ReactNode } from 'react';

interface User {
  id: number;
  username: string;
  realName: string;
  birthday: string;
  avatarUrl?: string;
  location?: {
    lat: number;
    lng: number;
    address: string;
  };
  lastActive?: string;
}

interface AppState {
  user: User | null;
  partner: User | null;
  isAuthenticated: boolean;
  isConnected: boolean; // WebSocket连接状态
  theme: 'light' | 'dark';
  notifications: Array<{
    id: string;
    type: 'success' | 'error' | 'info' | 'warning';
    message: string;
    timestamp: Date;
  }>;
  unreadCount: number;
}

type AppAction =
  | { type: 'SET_USER'; payload: User | null }
  | { type: 'SET_PARTNER'; payload: User | null }
  | { type: 'SET_AUTHENTICATED'; payload: boolean }
  | { type: 'SET_CONNECTED'; payload: boolean }
  | { type: 'SET_THEME'; payload: AppState['theme'] }
  | { type: 'ADD_NOTIFICATION'; payload: AppState['notifications'][0] }
  | { type: 'REMOVE_NOTIFICATION'; payload: string }
  | { type: 'CLEAR_NOTIFICATIONS' }
  | { type: 'SET_UNREAD_COUNT'; payload: number }
  | { type: 'UPDATE_USER_LOCATION'; payload: { lat: number; lng: number; address: string } }
  | { type: 'UPDATE_PARTNER_LOCATION'; payload: { lat: number; lng: number; address: string } };

const initialState: AppState = {
  user: null,
  partner: null,
  isAuthenticated: false,
  isConnected: false,
  theme: 'light',
  notifications: [],
  unreadCount: 0,
};

const appReducer = (state: AppState, action: AppAction): AppState => {
  switch (action.type) {
    case 'SET_USER':
      return { ...state, user: action.payload };
    case 'SET_PARTNER':
      return { ...state, partner: action.payload };
    case 'SET_AUTHENTICATED':
      return { ...state, isAuthenticated: action.payload };
    case 'SET_CONNECTED':
      return { ...state, isConnected: action.payload };
    case 'SET_THEME':
      return { ...state, theme: action.payload };
    case 'ADD_NOTIFICATION':
      return {
        ...state,
        notifications: [...state.notifications, action.payload]
      };
    case 'REMOVE_NOTIFICATION':
      return {
        ...state,
        notifications: state.notifications.filter(n => n.id !== action.payload),
      };
    case 'CLEAR_NOTIFICATIONS':
      return { ...state, notifications: [] };
    case 'SET_UNREAD_COUNT':
      return { ...state, unreadCount: action.payload };
    case 'UPDATE_USER_LOCATION':
      return {
        ...state,
        user: state.user ? { ...state.user, location: action.payload } : null
      };
    case 'UPDATE_PARTNER_LOCATION':
      return {
        ...state,
        partner: state.partner ? { ...state.partner, location: action.payload } : null
      };
    default:
      return state;
  }
};

interface AppContextType {
  state: AppState;
  dispatch: React.Dispatch<AppAction>;
  setUser: (user: AppState['user']) => void;
  setTheme: (theme: AppState['theme']) => void;
  addNotification: (notification: Omit<AppState['notifications'][0], 'id' | 'timestamp'>) => void;
  removeNotification: (id: string) => void;
  clearNotifications: () => void;
}

const AppContext = createContext<AppContextType | undefined>(undefined);

export const AppProvider: React.FC<{ children: ReactNode }> = ({ children }) => {
  const [state, dispatch] = useReducer(appReducer, initialState);

  const setUser = (user: AppState['user']) => {
    dispatch({ type: 'SET_USER', payload: user });
  };

  const setTheme = (theme: AppState['theme']) => {
    dispatch({ type: 'SET_THEME', payload: theme });
    localStorage.setItem('theme', theme);
  };

  const addNotification = (notification: Omit<AppState['notifications'][0], 'id' | 'timestamp'>) => {
    const newNotification = {
      ...notification,
      id: Date.now().toString(),
      timestamp: new Date(),
    };
    dispatch({ type: 'ADD_NOTIFICATION', payload: newNotification });
  };

  const removeNotification = (id: string) => {
    dispatch({ type: 'REMOVE_NOTIFICATION', payload: id });
  };

  const clearNotifications = () => {
    dispatch({ type: 'CLEAR_NOTIFICATIONS' });
  };

  const value: AppContextType = {
    state,
    dispatch,
    setUser,
    setTheme,
    addNotification,
    removeNotification,
    clearNotifications,
  };

  return <AppContext.Provider value={value}>{children}</AppContext.Provider>;
};

export const useAppContext = () => {
  const context = useContext(AppContext);
  if (context === undefined) {
    throw new Error('useAppContext must be used within an AppProvider');
  }
  return context;
};