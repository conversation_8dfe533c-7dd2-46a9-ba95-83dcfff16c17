{"name": "love-distance-backend", "version": "1.0.0", "description": "异地恋情侣网站后端API", "main": "server.js", "scripts": {"start": "node server.js", "dev": "nodemon server.js", "init-db": "node scripts/init-database.js", "test": "jest", "test:watch": "jest --watch", "test:coverage": "jest --coverage"}, "dependencies": {"bcrypt": "^6.0.0", "bcryptjs": "^2.4.3", "cors": "^2.8.5", "crypto-js": "^4.2.0", "dotenv": "^16.3.1", "express": "^4.18.2", "express-rate-limit": "^7.1.5", "express-validator": "^7.0.1", "helmet": "^7.1.0", "jsonwebtoken": "^9.0.2", "moment": "^2.29.4", "multer": "^1.4.5-lts.1", "mysql2": "^3.6.5", "socket.io": "^4.7.4"}, "devDependencies": {"nodemon": "^3.0.2", "jest": "^29.7.0", "supertest": "^6.3.3"}, "keywords": ["love", "couple", "distance", "relationship"], "author": "Love Distance Team", "license": "MIT"}