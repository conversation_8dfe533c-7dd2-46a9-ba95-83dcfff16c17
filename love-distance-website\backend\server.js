const express = require('express');
const cors = require('cors');
const helmet = require('helmet');
const rateLimit = require('express-rate-limit');
const http = require('http');
const socketIo = require('socket.io');
const path = require('path');
const multer = require('multer');
const fs = require('fs');
require('dotenv').config();

// 验证环境变量
require('./utils/env-validator');

const { testConnection, initDatabase } = require('./config/database');

const app = express();
const server = http.createServer(app);
const io = socketIo(server, {
  cors: {
    origin: process.env.FRONTEND_URL || "http://localhost:5173",
    methods: ["GET", "POST"],
    credentials: true
  }
});

// 中间件配置
app.use(helmet());
app.use(cors({
  origin: process.env.FRONTEND_URL || 'http://localhost:5173',
  credentials: true
}));

// 限流配置
const limiter = rateLimit({
  windowMs: 15 * 60 * 1000, // 15分钟
  max: 100, // 限制每个IP 15分钟内最多100个请求
  message: '请求过于频繁，请稍后再试'
});
app.use(limiter);

app.use(express.json({ limit: '10mb' }));
app.use(express.urlencoded({ extended: true, limit: '10mb' }));

// 静态文件服务
app.use('/uploads', express.static(path.join(__dirname, 'uploads')));

// 确保上传目录存在
const uploadDir = path.join(__dirname, 'uploads');
if (!fs.existsSync(uploadDir)) {
  fs.mkdirSync(uploadDir, { recursive: true });
}

// 文件上传配置
const storage = multer.diskStorage({
  destination: (req, file, cb) => {
    const uploadPath = path.join(__dirname, 'uploads');
    cb(null, uploadPath);
  },
  filename: (req, file, cb) => {
    const uniqueSuffix = Date.now() + '-' + Math.round(Math.random() * 1E9);
    cb(null, file.fieldname + '-' + uniqueSuffix + path.extname(file.originalname));
  }
});

const upload = multer({
  storage: storage,
  limits: {
    fileSize: parseInt(process.env.MAX_FILE_SIZE) || 10 * 1024 * 1024 // 10MB
  },
  fileFilter: (req, file, cb) => {
    const allowedTypes = (process.env.ALLOWED_FILE_TYPES || 'jpg,jpeg,png,gif').split(',');
    const fileExt = path.extname(file.originalname).toLowerCase().slice(1);
    
    if (allowedTypes.includes(fileExt)) {
      cb(null, true);
    } else {
      cb(new Error('不支持的文件类型'), false);
    }
  }
});

// 路由配置
app.use('/api/auth', require('./routes/auth'));
app.use('/api/users', require('./routes/users'));
app.use('/api/messages', require('./routes/messages'));
app.use('/api/albums', require('./routes/albums'));
app.use('/api/photos', require('./routes/photos'));
app.use('/api/anniversaries', require('./routes/anniversaries'));
app.use('/api/locations', require('./routes/locations'));

// WebSocket连接处理
const connectedUsers = new Map();
const { authenticateSocket, validateUserPermission, validateMessageData, validateLocationData } = require('./middleware/socket-auth');

// WebSocket身份验证
io.use(authenticateSocket);

io.on('connection', (socket) => {
  console.log('用户连接:', socket.id, '用户ID:', socket.userId);

  // 用户加入
  socket.on('join', () => {
    try {
      connectedUsers.set(socket.userId, socket.id);
      console.log(`用户 ${socket.userId} 已连接`);

      // 通知伙伴用户上线
      const partnerSocketId = connectedUsers.get(socket.partnerId);
      if (partnerSocketId) {
        io.to(partnerSocketId).emit('partner_online', {
          userId: socket.userId,
          username: socket.username,
          timestamp: new Date()
        });
      }
    } catch (error) {
      console.error('用户加入失败:', error);
      socket.emit('error', { message: '加入失败' });
    }
  });

  // 发送消息
  socket.on('send_message', (data) => {
    try {
      validateMessageData(data);

      if (!validateUserPermission(socket, data.receiverId)) {
        socket.emit('error', { message: '无权限发送消息给该用户' });
        return;
      }

      const receiverSocketId = connectedUsers.get(data.receiverId);

      if (receiverSocketId) {
        io.to(receiverSocketId).emit('receive_message', {
          senderId: socket.userId,
          message: data.message,
          messageType: data.messageType || 'text',
          timestamp: new Date()
        });
      }
    } catch (error) {
      console.error('发送消息失败:', error);
      socket.emit('error', { message: error.message });
    }
  });

  // 位置更新
  socket.on('location_update', (data) => {
    try {
      validateLocationData(data);

      if (!validateUserPermission(socket, data.receiverId)) {
        socket.emit('error', { message: '无权限发送位置给该用户' });
        return;
      }

      const receiverSocketId = connectedUsers.get(data.receiverId);

      if (receiverSocketId) {
        io.to(receiverSocketId).emit('partner_location_update', {
          senderId: socket.userId,
          location: data.location,
          timestamp: new Date()
        });
      }
    } catch (error) {
      console.error('位置更新失败:', error);
      socket.emit('error', { message: error.message });
    }
  });

  // 用户断开连接
  socket.on('disconnect', () => {
    if (socket.userId) {
      connectedUsers.delete(socket.userId);
      console.log(`用户 ${socket.userId} 已断开连接`);

      // 通知伙伴用户下线
      const partnerSocketId = connectedUsers.get(socket.partnerId);
      if (partnerSocketId) {
        io.to(partnerSocketId).emit('partner_offline', {
          userId: socket.userId,
          username: socket.username,
          timestamp: new Date()
        });
      }
    }
  });
});

// 错误处理中间件
app.use((error, req, res, next) => {
  console.error('服务器错误:', error);
  
  if (error instanceof multer.MulterError) {
    if (error.code === 'LIMIT_FILE_SIZE') {
      return res.status(400).json({ error: '文件大小超出限制' });
    }
  }
  
  res.status(500).json({ error: '服务器内部错误' });
});

// 404处理
app.use('*', (req, res) => {
  res.status(404).json({ error: '接口不存在' });
});

// 启动服务器
const PORT = process.env.PORT || 3001;

async function startServer() {
  try {
    // 测试数据库连接
    const dbConnected = await testConnection();
    if (!dbConnected) {
      console.error('❌ 数据库连接失败，服务器启动中止');
      process.exit(1);
    }

    // 初始化数据库表
    await initDatabase();

    // 启动服务器
    server.listen(PORT, () => {
      console.log(`🚀 服务器运行在端口 ${PORT}`);
      console.log(`📱 前端地址: http://localhost:5173`);
      console.log(`🔗 API地址: http://localhost:${PORT}/api`);
      console.log(`💾 数据库: ${process.env.DB_NAME}`);
    });

  } catch (error) {
    console.error('❌ 服务器启动失败:', error);
    process.exit(1);
  }
}

// 优雅关闭
process.on('SIGTERM', () => {
  console.log('收到SIGTERM信号，正在关闭服务器...');
  server.close(() => {
    console.log('服务器已关闭');
    process.exit(0);
  });
});

process.on('SIGINT', () => {
  console.log('收到SIGINT信号，正在关闭服务器...');
  server.close(() => {
    console.log('服务器已关闭');
    process.exit(0);
  });
});

startServer();

module.exports = { app, server, io };